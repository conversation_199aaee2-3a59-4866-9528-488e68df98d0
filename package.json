{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "db:push": "npx drizzle-kit push", "db:generate": "npx drizzle-kit generate", "db:migrate": "npx drizzle-kit migrate"}, "dependencies": {"@cubejs-client/core": "^1.3.49", "@internationalized/date": "^3.8.2", "@mastra/client-js": "0.10.14", "@mastra/core": "0.10.15", "@nuxt/eslint": "1.4.1", "@pinia/nuxt": "^0.11.1", "@tanstack/vue-table": "^8.21.3", "@tiptap/core": "^2.14.0", "@tiptap/pm": "^2.22.3", "@tiptap/starter-kit": "^2.14.0", "@tiptap/suggestion": "^2.25.0", "@tiptap/vue-3": "^2.22.3", "@vueuse/core": "^13.3.0", "@vueuse/motion": "^3.0.3", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.4", "echarts": "^5.6.0", "eslint": "^9.0.0", "eventemitter3": "^5.0.1", "flexsearch": "^0.8.205", "grid-layout-plus": "^1.1.0", "ipaddr.js": "^2.2.0", "jose": "^6.0.12", "js-cookie": "^3.0.5", "ldrs": "^1.1.7", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.514.0", "markdown-it": "^14.1.0", "marked": "^16.0.0", "mermaid": "^11.7.0", "morphdom": "^2.7.5", "nanoid": "^5.1.5", "nunjucks": "^3.2.4", "nuxt": "^3.17.5", "object-hash": "^3.0.0", "openai": "^5.3.0", "pg": "^8.16.3", "pinia": "^3.0.3", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "prismjs": "^1.30.0", "reka-ui": "^2.3.1", "rxjs": "^7.8.2", "sass": "^1.89.2", "shadcn-nuxt": "2.2.0", "tailwind-merge": "^3.3.1", "ts-results": "^3.3.0", "typescript": "^5.6.3", "unique-names-generator": "^4.7.1", "uuid": "^11.1.0", "v-viewer": "^3.0.22", "viewerjs": "^1.11.7", "vue": "^3.5.16", "vue-chartjs": "^5.3.2", "vue-datepicker-next": "^1.0.3", "vue-echarts": "^7.0.3", "vue-i18n": "^11.1.5", "vue-router": "^4.5.1", "vue-sonner": "^2.0.0", "vue-virtual-scroller": "2.0.0-beta.8", "yaml": "^2.8.0", "zod": "^3.25.67"}, "packageManager": "pnpm@9.7.0", "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.10", "@types/js-cookie": "^3.0.6", "@types/json-schema": "^7.0.15", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/nunjucks": "^3.2.6", "@types/object-hash": "^3.0.6", "@types/pg": "^8.15.5", "@types/prismjs": "^1.26.5", "drizzle-kit": "^0.31.4", "nuxt-mcp": "^0.2.2", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7", "tsx": "^4.20.3"}, "engines": {"node": ">=22.0.0", "pnpm": ">=9"}}